#!/usr/bin/env python3
"""
离线密钥格式验证脚本（不需要网络连接）
"""

import sys
import os
import re

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def validate_key_format(api_key: str) -> bool:
    """验证密钥格式是否正确"""
    # Gemini API密钥格式验证
    if not api_key:
        return False
    
    # 检查是否以正确的前缀开始
    if not api_key.startswith('AIzaSy'):
        return False
    
    # 检查长度（通常是39个字符）
    if len(api_key) != 39:
        return False
    
    # 检查是否只包含有效字符（字母、数字、-、_）
    if not re.match(r'^[A-Za-z0-9_-]+$', api_key):
        return False
    
    return True


def test_key_format(api_key: str):
    """测试密钥格式"""
    print(f"🔍 测试密钥格式: {api_key[:10]}...")
    print(f"📝 完整密钥: {api_key}")
    print("-" * 50)
    
    # 基本格式检查
    checks = {
        "前缀检查": api_key.startswith('AIzaSy'),
        "长度检查": len(api_key) == 39,
        "字符检查": re.match(r'^[A-Za-z0-9_-]+$', api_key) is not None,
        "整体格式": validate_key_format(api_key)
    }
    
    for check_name, result in checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {'通过' if result else '失败'}")
    
    overall_result = all(checks.values())
    
    if overall_result:
        print("\n🎉 密钥格式验证通过！")
        print("💡 这是一个格式正确的Gemini API密钥")
        return True
    else:
        print("\n❌ 密钥格式验证失败！")
        print("💡 请检查密钥是否正确")
        return False


def test_config_keys():
    """测试配置文件中的密钥"""
    print("🔧 测试配置文件中的密钥...")
    
    # 从环境变量读取
    test_key = os.getenv("TEST_GEMINI_KEY")
    
    if not test_key:
        print("❌ 未找到 TEST_GEMINI_KEY 环境变量")
        return False
    
    return test_key_format(test_key)


def main():
    """主函数"""
    print("🔑 离线密钥格式验证工具")
    print("=" * 50)
    print("ℹ️ 此工具只验证密钥格式，不需要网络连接")
    print()
    
    # 测试配置文件中的密钥
    result = test_config_keys()
    
    print("\n" + "=" * 50)
    print("📋 验证结果:")
    
    if result:
        print("✅ 密钥格式正确")
        print("💡 下一步:")
        print("   1. 配置代理服务器（如果需要）")
        print("   2. 确保网络能访问Google服务")
        print("   3. 重新运行在线测试")
    else:
        print("❌ 密钥格式有问题")
        print("💡 建议:")
        print("   1. 检查密钥是否完整")
        print("   2. 确认密钥来源是否正确")
        print("   3. 重新获取有效的Gemini API密钥")
    
    return 0 if result else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
