#!/usr/bin/env python3
"""
Google官方密钥联网测试脚本
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_google_official_key():
    """测试Google官方密钥"""
    # Google官方密钥
    api_key = "AIzaSyD8znBvlroO-qVf0kfOa3cxDzb3D5Fut2c"
    
    print("🔑 Google官方密钥联网测试")
    print("=" * 50)
    print(f"🔍 测试密钥: {api_key[:10]}...")
    print(f"📝 完整密钥: {api_key}")
    print(f"🌐 来源: Google官网")
    print("-" * 50)
    
    try:
        import google.generativeai as genai
        
        print("📦 导入Google AI库: ✅")
        
        # 配置API密钥
        genai.configure(api_key=api_key)
        print("🔧 配置API密钥: ✅")
        
        # 创建模型
        model = genai.GenerativeModel('gemini-2.5-flash')
        print("🤖 创建Gemini模型: ✅")
        
        print("\n⏳ 正在发送测试请求到Google服务器...")
        print("📡 请求地址: generativelanguage.googleapis.com")
        
        start_time = time.time()
        
        # 发送测试请求
        response = model.generate_content("Hello, please respond with 'API test successful'")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 响应时间: {duration:.2f}秒")
        
        if response and hasattr(response, 'text') and response.text:
            print("✅ 密钥验证成功！")
            print(f"📝 API响应: {response.text}")
            print("\n🎉 Google官方密钥可以正常使用！")
            return True
        else:
            print("❌ 响应无效或为空")
            print(f"📝 响应对象: {response}")
            return False
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time if 'start_time' in locals() else 0
        
        error_str = str(e).lower()
        print(f"⏱️ 测试耗时: {duration:.2f}秒")
        print(f"💥 发生错误: {e}")
        
        # 详细错误分析
        if "api key not valid" in error_str or "invalid api key" in error_str:
            print("\n❌ 密钥无效")
            print("💡 可能原因:")
            print("   1. 密钥已过期")
            print("   2. 密钥被撤销")
            print("   3. API服务未启用")
            return False
            
        elif "403" in str(e) or "permission denied" in error_str:
            print("\n❌ 权限被拒绝")
            print("💡 可能原因:")
            print("   1. API服务未启用")
            print("   2. 账户权限不足")
            print("   3. 配额已用完")
            return False
            
        elif "429" in str(e) or "rate limit" in error_str or "quota" in error_str:
            print("\n⚠️ 速率限制或配额问题")
            print("💡 可能原因:")
            print("   1. 请求过于频繁")
            print("   2. 免费配额已用完")
            print("   3. 需要等待配额重置")
            return None
            
        elif "service unavailable" in error_str or "503" in str(e) or "unavailable" in error_str:
            print("\n⚠️ 网络连接问题")
            print("💡 可能原因:")
            print("   1. 无法连接到Google服务器")
            print("   2. 网络防火墙阻止")
            print("   3. 需要配置代理")
            print("\n🌐 网络解决方案:")
            print("   1. 配置代理: PROXY=http://your-proxy:8080")
            print("   2. 使用VPN")
            print("   3. 检查防火墙设置")
            return None
            
        else:
            print(f"\n❓ 未知错误: {e}")
            return False


def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 网络连接测试:")
    
    try:
        import socket
        
        # 测试DNS解析
        host = "generativelanguage.googleapis.com"
        port = 443
        
        print(f"📡 测试连接: {host}:{port}")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✅ 网络连接正常")
            return True
        else:
            print("❌ 网络连接失败")
            print("💡 建议配置代理或使用VPN")
            return False
            
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔑 Google官方密钥联网验证工具")
    print("=" * 60)
    
    # 先测试网络连接
    network_ok = test_network_connectivity()
    
    if not network_ok:
        print("\n⚠️ 网络连接有问题，但仍然尝试API测试...")
    
    print("\n" + "=" * 60)
    
    # 测试API密钥
    result = test_google_official_key()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    if result:
        print("🎉 Google官方密钥验证成功！")
        print("✅ 密钥有效且可以正常使用")
        print("💡 可以开始使用系统进行密钥扫描")
        
    elif result is False:
        print("❌ Google官方密钥验证失败！")
        print("💡 建议:")
        print("   1. 检查密钥是否正确")
        print("   2. 确认Google AI API是否已启用")
        print("   3. 检查账户配额和权限")
        
    else:  # result is None
        print("⚠️ 验证结果不确定（网络或配额问题）")
        print("💡 建议:")
        print("   1. 配置代理服务器")
        print("   2. 稍后重试")
        print("   3. 检查API配额")
    
    return 0 if result else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
