# 🤖 Google/Gemini 配置说明

## 📋 核心配置

### 🔴 必填配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `GITHUB_TOKENS` | GitHub API令牌（用于搜索仓库） | `ghp_your_token_here` |
| `HAJIMI_CHECK_MODEL` | Gemini验证模型 | `gemini-2.5-flash` |

### 🤖 Google/Gemini 专用配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `HAJIMI_CHECK_MODEL` | `gemini-2.5-flash` | 用于验证密钥的Gemini模型 |
| `TEST_GEMINI_KEY` | - | 测试用的Gemini API密钥 |
| `VALIDATION_MAX_RETRIES` | `3` | Gemini API验证重试次数 |
| `VALIDATION_RETRY_DELAY_MIN` | `1.0` | 重试最小延迟（秒） |
| `VALIDATION_RETRY_DELAY_MAX` | `3.0` | 重试最大延迟（秒） |

### 🌐 网络配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `PROXY` | 代理服务器（访问Google服务时需要） | `http://proxy:8080` |

## 📝 当前配置文件

```env
# ========================================
# 🔴 必填配置
# ========================================

# GitHub API tokens (用于搜索GitHub仓库)
GITHUB_TOKENS=****************************************

# ========================================
# 🤖 Google/Gemini 配置
# ========================================

# 用于验证密钥有效性的Gemini模型
HAJIMI_CHECK_MODEL=gemini-2.5-flash

# 测试用的Gemini API密钥
TEST_GEMINI_KEY=AIzaSyCpE-_0je5_FM9D5ETQJ5SxpBO8HYtIYzI

# ========================================
# 🔧 基础配置
# ========================================

# 数据存储目录
DATA_PATH=/app/data

# 搜索范围（天数）
DATE_RANGE_DAYS=90

# 搜索查询文件
QUERIES_FILE=queries.txt

# 代理配置（如果需要访问Google服务）
PROXY=

# ========================================
# 🔄 Google API 验证重试配置
# ========================================

# Gemini API验证重试次数
VALIDATION_MAX_RETRIES=3

# 重试延迟时间（秒）
VALIDATION_RETRY_DELAY_MIN=1.0
VALIDATION_RETRY_DELAY_MAX=3.0

# 批量验证阈值
VALIDATION_BATCH_RETRY_THRESHOLD=5
```

## 🧪 测试您的配置

### 快速测试Gemini密钥
```bash
python tests/quick_key_test.py
```

### 完整配置检查
```bash
python scripts/check_config.py
```

## 🌐 网络问题解决

如果遇到网络连接问题，配置代理：

```env
# HTTP代理
PROXY=http://your-proxy:8080

# SOCKS5代理
PROXY=socks5://your-proxy:1080

# 带认证的代理
PROXY=****************************************
```

## 🎯 重要提示

1. **只关注Google服务**: 已禁用所有其他外部服务
2. **密钥安全**: 请妥善保管您的Gemini API密钥
3. **网络访问**: 确保能够访问 `generativelanguage.googleapis.com`
4. **模型选择**: 推荐使用 `gemini-2.5-flash` 进行验证

## 🚀 开始使用

配置完成后，运行主程序：

```bash
python app/hajimi_king.py
```

系统将自动：
1. 搜索GitHub仓库中的Gemini API密钥
2. 使用您配置的Gemini模型验证密钥
3. 应用重试机制处理网络问题
4. 保存有效的密钥到文件中
