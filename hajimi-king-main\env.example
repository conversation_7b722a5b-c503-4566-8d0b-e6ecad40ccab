# GitHub API tokens配置 (逗号分隔)
# 在 https://github.com/settings/tokens 创建token
# GitHub API tokens配置 (逗号分隔)
GITHUB_TOKENS=
# 请求代理，多个使用逗号分隔
PROXY=

DATA_PATH=/app/data
# 搜索查询文件路径 (相对于DATA_PATH)
QUERIES_FILE=queries.txt
HAJIMI_CHECK_MODEL=gemini-2.5-flash

# 文件前缀配置
VALID_KEY_PREFIX=keys/keys_valid_
RATE_LIMITED_KEY_PREFIX=keys/key_429_
KEYS_SEND_PREFIX=keys/keys_send_

VALID_KEY_DETAIL_PREFIX=logs/keys_valid_detail_
RATE_LIMITED_KEY_DETAIL_PREFIX=logs/key_429_detail_
KEYS_SEND_DETAIL_PREFIX=logs/keys_send_detail_

# 日期范围过滤器配置 (单位：天，过滤掉超过指定天数的旧仓库)
# 默认730天 (约2年)，可根据需要调整
DATE_RANGE_DAYS=730

GEMINI_BALANCER_SYNC_ENABLED=false
GEMINI_BALANCER_URL=
GEMINI_BALANCER_AUTH=

GPT_LOAD_SYNC_ENABLED=false
GPT_LOAD_URL=
GPT_LOAD_AUTH=
GPT_LOAD_GROUP_NAME=group1,group2,group3

# 文件路径黑名单配置 (逗号分隔，用于跳过文档和示例文件)
FILE_PATH_BLACKLIST=readme,docs,doc/,.md,example,sample,tutorial,test,spec,demo,mock
