#!/usr/bin/env python3
"""
配置检查和验证脚本
"""

import sys
import os
import re

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from common.config import Config


def check_required_config():
    """检查必填配置"""
    print("🔴 检查必填配置...")
    
    errors = []
    
    # 检查GitHub tokens
    if not Config.GITHUB_TOKENS:
        errors.append("❌ GITHUB_TOKENS 未配置")
    else:
        print(f"✅ GITHUB_TOKENS: 已配置 {len(Config.GITHUB_TOKENS)} 个token")
        
        # 验证token格式
        for i, token in enumerate(Config.GITHUB_TOKENS):
            if not token.startswith('ghp_') and not token.startswith('github_pat_'):
                errors.append(f"⚠️ Token {i+1} 格式可能不正确: {token[:10]}...")
    
    return errors


def check_important_config():
    """检查重要配置"""
    print("\n🟡 检查重要配置...")
    
    warnings = []
    
    # 检查数据路径
    if Config.DATA_PATH:
        print(f"✅ DATA_PATH: {Config.DATA_PATH}")
        if not os.path.exists(Config.DATA_PATH):
            warnings.append(f"⚠️ 数据目录不存在: {Config.DATA_PATH}")
    else:
        warnings.append("⚠️ DATA_PATH 未配置")
    
    # 检查日期范围
    print(f"✅ DATE_RANGE_DAYS: {Config.DATE_RANGE_DAYS} 天")
    if Config.DATE_RANGE_DAYS > 365:
        warnings.append(f"⚠️ DATE_RANGE_DAYS 设置较大({Config.DATE_RANGE_DAYS}天)，可能影响性能")
    
    # 检查模型配置
    print(f"✅ HAJIMI_CHECK_MODEL: {Config.HAJIMI_CHECK_MODEL}")
    
    # 检查代理配置
    if Config.PROXY_LIST:
        print(f"✅ PROXY: 已配置 {len(Config.PROXY_LIST)} 个代理")
        for i, proxy in enumerate(Config.PROXY_LIST):
            if not re.match(r'^(http|https|socks5)://', proxy):
                warnings.append(f"⚠️ 代理 {i+1} 格式可能不正确: {proxy}")
    else:
        print("ℹ️ PROXY: 未配置（如果网络访问正常可忽略）")
    
    return warnings


def check_retry_config():
    """检查重试配置"""
    print("\n🔄 检查验证重试配置...")
    
    suggestions = []
    
    print(f"✅ VALIDATION_MAX_RETRIES: {Config.VALIDATION_MAX_RETRIES}")
    if Config.VALIDATION_MAX_RETRIES < 1:
        suggestions.append("⚠️ 重试次数至少应该为1")
    elif Config.VALIDATION_MAX_RETRIES > 10:
        suggestions.append("⚠️ 重试次数过多可能影响性能")
    
    print(f"✅ VALIDATION_RETRY_DELAY_MIN: {Config.VALIDATION_RETRY_DELAY_MIN}秒")
    print(f"✅ VALIDATION_RETRY_DELAY_MAX: {Config.VALIDATION_RETRY_DELAY_MAX}秒")
    
    if Config.VALIDATION_RETRY_DELAY_MIN >= Config.VALIDATION_RETRY_DELAY_MAX:
        suggestions.append("⚠️ 重试延迟最小值应该小于最大值")
    
    print(f"✅ VALIDATION_BATCH_RETRY_THRESHOLD: {Config.VALIDATION_BATCH_RETRY_THRESHOLD}")
    
    return suggestions


def check_external_services():
    """检查外部服务配置"""
    print("\n🔗 检查外部服务配置...")
    
    # Gemini Balancer
    if Config.parse_bool(Config.GEMINI_BALANCER_SYNC_ENABLED):
        print("✅ Gemini Balancer: 已启用")
        if not Config.GEMINI_BALANCER_URL:
            print("❌ GEMINI_BALANCER_URL 未配置")
        else:
            print(f"✅ GEMINI_BALANCER_URL: {Config.GEMINI_BALANCER_URL}")
        
        if not Config.GEMINI_BALANCER_AUTH:
            print("❌ GEMINI_BALANCER_AUTH 未配置")
        else:
            print("✅ GEMINI_BALANCER_AUTH: 已配置")
    else:
        print("ℹ️ Gemini Balancer: 未启用")
    
    # GPT Load Balancer
    if Config.parse_bool(Config.GPT_LOAD_SYNC_ENABLED):
        print("✅ GPT Load Balancer: 已启用")
        if not Config.GPT_LOAD_URL:
            print("❌ GPT_LOAD_URL 未配置")
        else:
            print(f"✅ GPT_LOAD_URL: {Config.GPT_LOAD_URL}")
        
        if not Config.GPT_LOAD_AUTH:
            print("❌ GPT_LOAD_AUTH 未配置")
        else:
            print("✅ GPT_LOAD_AUTH: 已配置")
        
        if not Config.GPT_LOAD_GROUP_NAME:
            print("❌ GPT_LOAD_GROUP_NAME 未配置")
        else:
            print(f"✅ GPT_LOAD_GROUP_NAME: {Config.GPT_LOAD_GROUP_NAME}")
    else:
        print("ℹ️ GPT Load Balancer: 未启用")


def check_advanced_config():
    """检查高级配置"""
    print("\n🟢 检查高级配置...")
    
    print(f"✅ VALID_KEY_PREFIX: {Config.VALID_KEY_PREFIX}")
    print(f"✅ RATE_LIMITED_KEY_PREFIX: {Config.RATE_LIMITED_KEY_PREFIX}")
    print(f"✅ KEYS_SEND_PREFIX: {Config.KEYS_SEND_PREFIX}")
    print(f"✅ SCANNED_SHAS_FILE: {Config.SCANNED_SHAS_FILE}")
    
    # 检查文件路径黑名单
    blacklist_count = len(Config.FILE_PATH_BLACKLIST)
    print(f"✅ FILE_PATH_BLACKLIST: {blacklist_count} 项")
    if blacklist_count < 5:
        print("⚠️ 文件路径黑名单项目较少，可能会扫描到无关文件")


def generate_config_recommendations():
    """生成配置建议"""
    print("\n💡 配置建议:")
    
    print("\n📊 根据不同场景的推荐配置:")
    
    print("\n🚀 高效率场景（网络稳定）:")
    print("VALIDATION_MAX_RETRIES=2")
    print("VALIDATION_RETRY_DELAY_MIN=0.5")
    print("VALIDATION_RETRY_DELAY_MAX=2.0")
    print("DATE_RANGE_DAYS=30")
    
    print("\n🛡️ 高成功率场景（网络不稳定）:")
    print("VALIDATION_MAX_RETRIES=5")
    print("VALIDATION_RETRY_DELAY_MIN=2.0")
    print("VALIDATION_RETRY_DELAY_MAX=5.0")
    print("DATE_RANGE_DAYS=90")
    print("PROXY=http://your-proxy:8080")
    
    print("\n⚖️ 平衡场景（默认推荐）:")
    print("VALIDATION_MAX_RETRIES=3")
    print("VALIDATION_RETRY_DELAY_MIN=1.0")
    print("VALIDATION_RETRY_DELAY_MAX=3.0")
    print("DATE_RANGE_DAYS=90")


def main():
    """主函数"""
    print("🔧 配置检查和验证工具")
    print("=" * 50)
    
    # 检查各项配置
    errors = check_required_config()
    warnings = check_important_config()
    suggestions = check_retry_config()
    
    check_external_services()
    check_advanced_config()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 检查结果汇总:")
    
    if errors:
        print(f"\n❌ 发现 {len(errors)} 个错误:")
        for error in errors:
            print(f"  {error}")
    
    if warnings:
        print(f"\n⚠️ 发现 {len(warnings)} 个警告:")
        for warning in warnings:
            print(f"  {warning}")
    
    if suggestions:
        print(f"\n💡 发现 {len(suggestions)} 个建议:")
        for suggestion in suggestions:
            print(f"  {suggestion}")
    
    if not errors and not warnings and not suggestions:
        print("\n🎉 配置检查通过，没有发现问题！")
    
    # 生成建议
    generate_config_recommendations()
    
    # 返回状态码
    return len(errors)


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
