# ⚙️ 配置变量说明 📖

以下是所有可配置的环境变量，在 `.env` 文件中设置：

## 🔴 必填配置 ⚠️

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `GITHUB_TOKENS` | GitHub API访问令牌，多个用逗号分隔 🎫<br/>在 [GitHub Settings](https://github.com/settings/tokens) 创建，需要 `public_repo` 权限 | `ghp_token1,ghp_token2` |

## 🟡 重要配置（建议了解）🤓

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `PROXY` | 空 | 代理服务器地址，支持多个（逗号分隔）和账密认证 🌐<br/>格式：`**********************:port` 或 `socks5://user:pass@proxy:port` |
| `DATA_PATH` | `/app/data` | 数据存储目录路径 📂 |
| `DATE_RANGE_DAYS` | `730` | 仓库年龄过滤（天数），只扫描指定天数内有更新的仓库 📅 |
| `QUERIES_FILE` | `queries.txt` | 搜索查询配置文件路径（相对于DATA_PATH）🎯<br/>⚠️ 搜索表达式严重影响搜索效率 |
| `HAJIMI_CHECK_MODEL` | `gemini-2.5-flash` | 用于验证密钥有效性的Gemini模型 🤖 |

## 🔗 外部服务同步配置（可选功能）

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `GEMINI_BALANCER_SYNC_ENABLED` | `false` | 是否启用Gemini Balancer同步 🔗 |
| `GEMINI_BALANCER_URL` | 空 | Gemini Balancer服务地址 🌐<br/>例：`http://your-gemini-balancer.com` |
| `GEMINI_BALANCER_AUTH` | 空 | Gemini Balancer认证信息（密码）🔐 |
| `GPT_LOAD_SYNC_ENABLED` | `false` | 是否启用GPT Load Balancer同步 🔗 |
| `GPT_LOAD_URL` | 空 | GPT Load Balancer服务地址 🌐<br/>例：`http://your-gpt-load.com` |
| `GPT_LOAD_AUTH` | 空 | GPT Load Balancer认证Token（页面密码）🔐 |
| `GPT_LOAD_GROUP_NAME` | 空 | GPT Load Balancer组名，多个用逗号分隔 👥<br/>例：`group1,group2,group3` |

## 🔄 验证重试配置（新增功能）

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VALIDATION_MAX_RETRIES` | `3` | 最大重试次数 🔄<br/>推荐范围：2-5，网络不稳定时可增加 |
| `VALIDATION_RETRY_DELAY_MIN` | `1.0` | 重试最小延迟（秒）⏱️ |
| `VALIDATION_RETRY_DELAY_MAX` | `3.0` | 重试最大延迟（秒）⏱️ |
| `VALIDATION_BATCH_RETRY_THRESHOLD` | `5` | 批量验证重试阈值 📊<br/>当密钥数量超过此值时减少重试次数以提高效率 |

## 🟢 高级配置（不懂就别动）😅

### 文件名前缀配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VALID_KEY_PREFIX` | `keys/keys_valid_` | 有效密钥文件名前缀 🗝️ |
| `RATE_LIMITED_KEY_PREFIX` | `keys/key_429_` | 频率限制密钥文件名前缀 ⏰ |
| `KEYS_SEND_PREFIX` | `keys/keys_send_` | 发送到外部应用的密钥文件名前缀 🚀 |

### 详细日志配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VALID_KEY_DETAIL_PREFIX` | `logs/keys_valid_detail_` | 有效密钥详细日志文件名前缀 📝 |
| `RATE_LIMITED_KEY_DETAIL_PREFIX` | `logs/key_429_detail_` | 频率限制详细日志文件名前缀 📊 |
| `KEYS_SEND_DETAIL_PREFIX` | `logs/keys_send_detail_` | 密钥发送详细日志文件名前缀 📤 |

### 其他高级配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SCANNED_SHAS_FILE` | `scanned_shas.txt` | 已扫描文件SHA记录文件名 📋 |
| `FILE_PATH_BLACKLIST` | `readme,docs,doc/,.md,example,...` | 文件路径黑名单，跳过这些路径的文件（逗号分隔）🚫 |

## 📝 配置示例

### 基础配置示例
```env
# 必填配置
GITHUB_TOKENS=ghp_your_token_here

# 基础配置
DATA_PATH=/app/data
DATE_RANGE_DAYS=90
HAJIMI_CHECK_MODEL=gemini-2.5-flash
```

### 高性能配置示例
```env
# 使用代理提高访问速度
PROXY=http://proxy1:8080,http://proxy2:8080

# 增加重试次数提高成功率
VALIDATION_MAX_RETRIES=5
VALIDATION_RETRY_DELAY_MIN=0.5
VALIDATION_RETRY_DELAY_MAX=2.0

# 扩大搜索范围
DATE_RANGE_DAYS=365
```

### 外部服务集成示例
```env
# 启用Gemini Balancer同步
GEMINI_BALANCER_SYNC_ENABLED=true
GEMINI_BALANCER_URL=http://your-balancer.com
GEMINI_BALANCER_AUTH=your_password

# 启用GPT Load Balancer同步
GPT_LOAD_SYNC_ENABLED=true
GPT_LOAD_URL=http://your-gpt-load.com
GPT_LOAD_AUTH=your_token
GPT_LOAD_GROUP_NAME=production,backup
```

## ⚠️ 重要提示

1. **GitHub Token权限**：确保token具有 `public_repo` 权限
2. **代理配置**：如果网络访问GitHub有问题，建议配置代理
3. **重试配置**：网络不稳定时可适当增加重试次数和延迟
4. **搜索范围**：`DATE_RANGE_DAYS` 影响搜索效率，根据需要调整
5. **文件路径黑名单**：避免扫描文档、示例等无关文件，提高效率

## 🔧 配置验证

运行以下命令验证配置是否正确：

```bash
python tests/test_config.py
```

## 📞 技术支持

如果配置有问题，请检查：
1. `.env` 文件格式是否正确
2. GitHub Token是否有效
3. 代理设置是否正确
4. 网络连接是否正常
