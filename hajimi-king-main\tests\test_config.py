#!/usr/bin/env python3
"""
配置验证脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from common.config import Config


def test_retry_config():
    """测试重试配置是否正确加载"""
    print("🔧 测试重试配置...")
    
    print(f"✅ 最大重试次数: {Config.VALIDATION_MAX_RETRIES}")
    print(f"✅ 重试延迟最小值: {Config.VALIDATION_RETRY_DELAY_MIN}秒")
    print(f"✅ 重试延迟最大值: {Config.VALIDATION_RETRY_DELAY_MAX}秒")
    print(f"✅ 批量重试阈值: {Config.VALIDATION_BATCH_RETRY_THRESHOLD}")
    
    # 验证配置合理性
    assert Config.VALIDATION_MAX_RETRIES >= 1, "最大重试次数应该至少为1"
    assert Config.VALIDATION_RETRY_DELAY_MIN > 0, "重试延迟最小值应该大于0"
    assert Config.VALIDATION_RETRY_DELAY_MAX >= Config.VALIDATION_RETRY_DELAY_MIN, "重试延迟最大值应该大于等于最小值"
    assert Config.VALIDATION_BATCH_RETRY_THRESHOLD >= 1, "批量重试阈值应该至少为1"
    
    print("✅ 所有配置验证通过!")


def test_validation_function_import():
    """测试验证函数是否可以正确导入"""
    print("\n🔧 测试验证函数导入...")
    
    try:
        from app.hajimi_king import validate_gemini_key
        print("✅ validate_gemini_key 函数导入成功")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(validate_gemini_key)
        params = list(sig.parameters.keys())
        print(f"✅ 函数参数: {params}")
        
        # 验证参数
        assert 'api_key' in params, "应该有 api_key 参数"
        assert 'max_retries' in params, "应该有 max_retries 参数"
        
        print("✅ 函数签名验证通过!")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    return True


def test_env_file():
    """测试.env文件是否包含新配置"""
    print("\n🔧 测试.env文件配置...")
    
    env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
    
    if not os.path.exists(env_file):
        print("⚠️ .env文件不存在")
        return False
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_configs = [
        'VALIDATION_MAX_RETRIES',
        'VALIDATION_RETRY_DELAY_MIN',
        'VALIDATION_RETRY_DELAY_MAX',
        'VALIDATION_BATCH_RETRY_THRESHOLD'
    ]
    
    for config in required_configs:
        if config in content:
            print(f"✅ 找到配置: {config}")
        else:
            print(f"⚠️ 缺少配置: {config}")
    
    print("✅ .env文件检查完成!")
    return True


if __name__ == "__main__":
    print("🧪 开始配置验证测试...\n")
    
    try:
        test_retry_config()
        test_validation_function_import()
        test_env_file()
        
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
