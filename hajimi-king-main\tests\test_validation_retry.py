#!/usr/bin/env python3
"""
验证重试机制测试脚本
"""

import sys
import os
import time
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.hajimi_king import validate_gemini_key
from common.config import Config
import google.generativeai.types.generation_types as google_exceptions


class TestValidationRetry(unittest.TestCase):
    """验证重试机制测试类"""

    def setUp(self):
        """测试前设置"""
        # 保存原始配置
        self.original_max_retries = Config.VALIDATION_MAX_RETRIES
        self.original_delay_min = Config.VALIDATION_RETRY_DELAY_MIN
        self.original_delay_max = Config.VALIDATION_RETRY_DELAY_MAX
        
        # 设置测试配置
        Config.VALIDATION_MAX_RETRIES = 3
        Config.VALIDATION_RETRY_DELAY_MIN = 0.1
        Config.VALIDATION_RETRY_DELAY_MAX = 0.2

    def tearDown(self):
        """测试后清理"""
        # 恢复原始配置
        Config.VALIDATION_MAX_RETRIES = self.original_max_retries
        Config.VALIDATION_RETRY_DELAY_MIN = self.original_delay_min
        Config.VALIDATION_RETRY_DELAY_MAX = self.original_delay_max

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_successful_validation_first_try(self, mock_sleep, mock_genai):
        """测试第一次尝试就成功的情况"""
        # 模拟成功的响应
        mock_model = MagicMock()
        mock_response = MagicMock()
        mock_response.text = "Hello"
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        result = validate_gemini_key("test_key_123")
        
        self.assertEqual(result, "ok")
        # 第一次尝试成功，应该只调用一次sleep（初始延迟）
        self.assertEqual(mock_sleep.call_count, 1)

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_retry_on_rate_limit(self, mock_sleep, mock_genai):
        """测试速率限制时的重试机制"""
        mock_model = MagicMock()
        
        # 前两次抛出速率限制异常，第三次成功
        side_effects = [
            google_exceptions.TooManyRequests("Rate limit exceeded"),
            google_exceptions.TooManyRequests("Rate limit exceeded"),
            MagicMock(text="Hello")  # 成功响应
        ]
        mock_model.generate_content.side_effect = side_effects
        mock_genai.GenerativeModel.return_value = mock_model
        
        result = validate_gemini_key("test_key_123")
        
        self.assertEqual(result, "ok")
        # 应该调用3次sleep：初始延迟 + 2次重试延迟
        self.assertEqual(mock_sleep.call_count, 3)

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_max_retries_exceeded(self, mock_sleep, mock_genai):
        """测试超过最大重试次数的情况"""
        mock_model = MagicMock()
        
        # 所有尝试都失败
        mock_model.generate_content.side_effect = google_exceptions.TooManyRequests("Rate limit exceeded")
        mock_genai.GenerativeModel.return_value = mock_model
        
        result = validate_gemini_key("test_key_123")
        
        self.assertEqual(result, "rate_limited")
        # 应该调用3次sleep：初始延迟 + 2次重试延迟（第3次重试后不再sleep）
        self.assertEqual(mock_sleep.call_count, 3)

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_no_retry_on_auth_error(self, mock_sleep, mock_genai):
        """测试认证错误时不重试"""
        mock_model = MagicMock()
        mock_model.generate_content.side_effect = google_exceptions.PermissionDenied("Invalid API key")
        mock_genai.GenerativeModel.return_value = mock_model
        
        result = validate_gemini_key("test_key_123")
        
        self.assertEqual(result, "not_authorized_key")
        # 认证错误不重试，只有初始延迟
        self.assertEqual(mock_sleep.call_count, 1)

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_retry_on_network_error(self, mock_sleep, mock_genai):
        """测试网络错误时的重试机制"""
        mock_model = MagicMock()
        
        # 前两次网络错误，第三次成功
        side_effects = [
            ConnectionError("Network timeout"),
            ConnectionError("Connection failed"),
            MagicMock(text="Hello")  # 成功响应
        ]
        mock_model.generate_content.side_effect = side_effects
        mock_genai.GenerativeModel.return_value = mock_model
        
        result = validate_gemini_key("test_key_123")
        
        self.assertEqual(result, "ok")
        # 应该调用3次sleep：初始延迟 + 2次重试延迟
        self.assertEqual(mock_sleep.call_count, 3)

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_custom_max_retries(self, mock_sleep, mock_genai):
        """测试自定义最大重试次数"""
        mock_model = MagicMock()
        mock_model.generate_content.side_effect = google_exceptions.TooManyRequests("Rate limit exceeded")
        mock_genai.GenerativeModel.return_value = mock_model
        
        # 使用自定义重试次数
        result = validate_gemini_key("test_key_123", max_retries=2)
        
        self.assertEqual(result, "rate_limited")
        # 应该调用2次sleep：初始延迟 + 1次重试延迟
        self.assertEqual(mock_sleep.call_count, 2)

    @patch('app.hajimi_king.genai')
    @patch('app.hajimi_king.time.sleep')
    def test_invalid_response_retry(self, mock_sleep, mock_genai):
        """测试无效响应时的重试机制"""
        mock_model = MagicMock()
        
        # 前两次返回无效响应，第三次成功
        side_effects = [
            MagicMock(text=None),  # 无效响应
            None,  # 无效响应
            MagicMock(text="Hello")  # 成功响应
        ]
        mock_model.generate_content.side_effect = side_effects
        mock_genai.GenerativeModel.return_value = mock_model
        
        result = validate_gemini_key("test_key_123")
        
        self.assertEqual(result, "ok")
        # 应该调用3次sleep：初始延迟 + 2次重试延迟
        self.assertEqual(mock_sleep.call_count, 3)


def run_manual_test():
    """手动测试函数，用于实际验证"""
    print("🧪 开始手动测试验证重试机制...")
    
    # 测试无效密钥（应该不重试）
    print("\n1. 测试无效密钥（不应重试）:")
    start_time = time.time()
    result = validate_gemini_key("invalid_key_123", max_retries=2)
    end_time = time.time()
    print(f"   结果: {result}")
    print(f"   耗时: {end_time - start_time:.2f}秒")
    
    # 测试配置读取
    print(f"\n2. 当前配置:")
    print(f"   最大重试次数: {Config.VALIDATION_MAX_RETRIES}")
    print(f"   重试延迟范围: {Config.VALIDATION_RETRY_DELAY_MIN}-{Config.VALIDATION_RETRY_DELAY_MAX}秒")
    print(f"   批量阈值: {Config.VALIDATION_BATCH_RETRY_THRESHOLD}")
    
    print("\n✅ 手动测试完成")


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 单元测试")
    print("2. 手动测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        unittest.main()
    elif choice == "2":
        run_manual_test()
    else:
        print("无效选择")
