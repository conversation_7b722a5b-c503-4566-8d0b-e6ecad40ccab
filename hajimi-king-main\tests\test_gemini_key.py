#!/usr/bin/env python3
"""
Gemini API密钥测试脚本
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.hajimi_king import validate_gemini_key
from common.config import Config


def test_specific_key(api_key: str):
    """测试指定的API密钥"""
    print(f"🔍 测试密钥: {api_key[:10]}...")
    print(f"📝 完整密钥: {api_key}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # 使用我们优化后的验证函数
        result = validate_gemini_key(api_key, max_retries=3)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 验证耗时: {duration:.2f}秒")
        print(f"📊 验证结果: {result}")
        
        if result == "ok":
            print("✅ 密钥验证成功！这是一个有效的Gemini API密钥")
            return True
        elif "rate_limited" in result:
            print("⚠️ 密钥可能有效，但遇到了速率限制")
            print("💡 建议: 稍后再试或配置代理")
            return None
        elif result == "not_authorized_key":
            print("❌ 密钥无效或未授权")
            return False
        elif result == "disabled":
            print("❌ API服务未启用或被禁用")
            return False
        elif "network_error" in result:
            print("⚠️ 网络连接问题")
            print("💡 建议: 检查网络连接或配置代理")
            return None
        else:
            print(f"❓ 未知错误: {result}")
            return False
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 验证耗时: {duration:.2f}秒")
        print(f"💥 验证过程中发生异常: {e}")
        return False


def test_config_key():
    """测试配置文件中的密钥"""
    test_key = os.getenv("TEST_GEMINI_KEY")
    
    if not test_key:
        print("❌ 未在环境变量中找到 TEST_GEMINI_KEY")
        return False
    
    print("🔧 从配置文件读取测试密钥...")
    return test_specific_key(test_key)


def test_multiple_scenarios():
    """测试多种场景"""
    print("🧪 开始多场景测试...")
    
    # 测试配置文件中的密钥
    print("\n1️⃣ 测试配置文件中的密钥:")
    config_result = test_config_key()
    
    # 测试无效密钥（用于对比）
    print("\n2️⃣ 测试无效密钥（对比测试）:")
    invalid_result = test_specific_key("AIzaSyInvalidKeyForTesting123456789")
    
    # 测试空密钥
    print("\n3️⃣ 测试空密钥（对比测试）:")
    try:
        empty_result = test_specific_key("")
    except Exception as e:
        print(f"💥 空密钥测试异常（预期）: {e}")
        empty_result = False
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print(f"✅ 配置密钥测试: {'通过' if config_result else '失败' if config_result is False else '未确定'}")
    print(f"❌ 无效密钥测试: {'符合预期' if not invalid_result else '异常'}")
    print(f"❌ 空密钥测试: {'符合预期' if not empty_result else '异常'}")
    
    return config_result


def show_config_info():
    """显示当前配置信息"""
    print("⚙️ 当前配置信息:")
    print(f"📊 最大重试次数: {Config.VALIDATION_MAX_RETRIES}")
    print(f"⏱️ 重试延迟范围: {Config.VALIDATION_RETRY_DELAY_MIN}-{Config.VALIDATION_RETRY_DELAY_MAX}秒")
    print(f"📈 批量重试阈值: {Config.VALIDATION_BATCH_RETRY_THRESHOLD}")
    print(f"🤖 验证模型: {Config.HAJIMI_CHECK_MODEL}")
    
    if Config.PROXY_LIST:
        print(f"🌐 代理配置: {len(Config.PROXY_LIST)} 个代理")
    else:
        print("🌐 代理配置: 未配置")


def main():
    """主函数"""
    print("🔑 Gemini API密钥测试工具")
    print("="*50)
    
    # 显示配置信息
    show_config_info()
    print()
    
    # 运行测试
    result = test_multiple_scenarios()
    
    print("\n" + "="*50)
    if result:
        print("🎉 密钥测试成功！您的Gemini API密钥可以正常使用")
        print("💡 建议: 可以开始使用系统进行密钥扫描")
    elif result is False:
        print("❌ 密钥测试失败！请检查密钥是否正确")
        print("💡 建议: ")
        print("   1. 确认密钥是否正确")
        print("   2. 检查Gemini API是否已启用")
        print("   3. 确认账户是否有足够的配额")
    else:
        print("⚠️ 密钥测试结果不确定")
        print("💡 建议: ")
        print("   1. 检查网络连接")
        print("   2. 配置代理（如果需要）")
        print("   3. 稍后重试")
    
    return 0 if result else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
