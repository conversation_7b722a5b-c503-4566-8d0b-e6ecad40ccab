#!/usr/bin/env python3
"""
快速密钥测试脚本
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def quick_test_key(api_key: str):
    """快速测试API密钥"""
    print(f"🔍 快速测试密钥: {api_key[:10]}...")
    
    try:
        import google.generativeai as genai
        
        # 配置API密钥
        genai.configure(api_key=api_key)
        
        # 创建模型
        model = genai.GenerativeModel('gemini-2.5-flash')
        
        print("⏳ 正在发送测试请求...")
        start_time = time.time()
        
        # 发送简单的测试请求
        response = model.generate_content("Hello")
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response and hasattr(response, 'text') and response.text:
            print(f"✅ 密钥验证成功！")
            print(f"⏱️ 响应时间: {duration:.2f}秒")
            print(f"📝 响应内容: {response.text[:100]}...")
            return True
        else:
            print(f"❌ 响应无效")
            return False
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        error_str = str(e).lower()
        print(f"⏱️ 测试耗时: {duration:.2f}秒")
        
        if "api key not valid" in error_str or "invalid api key" in error_str:
            print(f"❌ 密钥无效: {e}")
            return False
        elif "403" in str(e) or "permission denied" in error_str:
            print(f"❌ 权限被拒绝: {e}")
            return False
        elif "429" in str(e) or "rate limit" in error_str:
            print(f"⚠️ 速率限制: {e}")
            return None
        elif "service unavailable" in error_str or "503" in str(e):
            print(f"⚠️ 服务不可用（可能是网络问题）: {e}")
            return None
        else:
            print(f"❓ 其他错误: {e}")
            return False


def main():
    """主函数"""
    print("🚀 快速密钥测试工具")
    print("=" * 40)
    
    # 测试您提供的密钥
    test_key = "AIzaSyCpE-_0je5_FM9D5ETQJ5SxpBO8HYtIYzI"
    
    result = quick_test_key(test_key)
    
    print("\n" + "=" * 40)
    if result:
        print("🎉 密钥测试成功！您的Gemini API密钥可以正常使用")
        print("💡 建议: 可以将此密钥用于系统验证")
    elif result is False:
        print("❌ 密钥测试失败！")
        print("💡 可能的原因:")
        print("   1. 密钥格式不正确")
        print("   2. 密钥已过期或被撤销")
        print("   3. API服务未启用")
    else:
        print("⚠️ 密钥测试结果不确定")
        print("💡 可能的原因:")
        print("   1. 网络连接问题")
        print("   2. 服务临时不可用")
        print("   3. 速率限制")
    
    return 0 if result else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
